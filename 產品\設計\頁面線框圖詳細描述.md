# 感恩日記App - 頁面線框圖詳細描述

## 1. 啟動頁 (Splash Screen)

### 佈局描述
```
┌─────────────────────────────────┐
│                                 │
│                                 │
│              LOGO               │ ← 金色感恩符號 (80x80px)
│           感恩日記              │ ← H1樣式，白色文字
│                                 │
│                                 │
│         感恩每一刻              │ ← Body/Large，白色文字
│                                 │
│                                 │
│                                 │
│                                 │
└─────────────────────────────────┘
```

### 設計細節
- **背景**: 紫色漸變 (#6B46C1 → #A78BFA)
- **Logo**: 居中，金色 #F59E0B
- **動畫**: Logo淡入 + 輕微縮放效果
- **持續時間**: 2秒後自動跳轉

## 2. 引導頁面 (Onboarding)

### 第一頁 - 歡迎
```
┌─────────────────────────────────┐
│                    跳過         │ ← 右上角文字按鈕
│                                 │
│                                 │
│           [插圖區域]            │ ← 金色線條插圖 (200x200px)
│                                 │
│                                 │
│        開始你的感恩之旅         │ ← H2樣式
│                                 │
│     記錄生活中的美好時刻        │ ← Body/Medium，中灰色
│     培養感恩的心，提升幸福感     │
│                                 │
│                                 │
│     ● ○ ○                      │ ← 進度指示器
│                                 │
│         [下一步按鈕]            │ ← 主要按鈕
└─────────────────────────────────┘
```

### 第二頁 - 功能介紹
```
┌─────────────────────────────────┐
│                    跳過         │
│                                 │
│                                 │
│           [功能插圖]            │ ← 卡片流動動畫插圖
│                                 │
│                                 │
│        發現感恩的力量           │ ← H2樣式
│                                 │
│     與他人分享你的感恩故事      │ ← Body/Medium
│     在社群中找到情感共鳴        │
│                                 │
│                                 │
│     ○ ● ○                      │ ← 進度指示器
│                                 │
│         [下一步按鈕]            │
└─────────────────────────────────┘
```

### 第三頁 - 專注功能
```
┌─────────────────────────────────┐
│                    跳過         │
│                                 │
│                                 │
│           [計時器插圖]          │ ← 圓形計時器插圖
│                                 │
│                                 │
│        保持專注與平衡           │ ← H2樣式
│                                 │
│     工作專注，適時休息          │ ← Body/Medium
│     感謝身心的協調配合          │
│                                 │
│                                 │
│     ○ ○ ●                      │ ← 進度指示器
│                                 │
│         [開始使用按鈕]          │
└─────────────────────────────────┘
```

## 3. 首頁 - 情感流動卡片牆

### 完整佈局
```
┌─────────────────────────────────┐
│  🔍    感恩日記    ⚙️          │ ← Navigation Bar (44px)
├─────────────────────────────────┤
│ 😊 😌 💖 🌟 🙏 ➡️             │ ← 情感篩選條 (60px)
├─────────────────────────────────┤
│                                 │
│  ┌──────────┐  ┌──────────┐    │ ← 卡片區域開始
│  │   卡片1   │  │   卡片2   │    │
│  │          │  │          │    │
│  │ 👤 用戶名 │  │ 👤 用戶名 │    │
│  │ 📅 2小時前│  │ 📅 5小時前│    │
│  │          │  │          │    │
│  │ 今天陽光很│  │ 感謝家人的│    │
│  │ 溫暖，感謝│  │ 陪伴...  │    │
│  │ 大自然... │  │          │    │
│  │          │  │ [圖片]    │    │
│  │ 😊 感恩   │  │ 💖 愛     │    │
│  │ ❤️ 12 💬 3│  │ ❤️ 8 💬 2 │    │
│  └──────────┘  └──────────┘    │
│                                 │
│  ┌──────────┐  ┌──────────┐    │
│  │   卡片3   │  │   卡片4   │    │
│  │          │  │          │    │
│  └──────────┘  └──────────┘    │
│                                 │
├─────────────────────────────────┤
│  🏠   ✏️   🎯   👤            │ ← Tab Bar (83px)
│ 首頁  記錄  專注  我的         │
└─────────────────────────────────┘
```

### 卡片詳細設計
```
┌──────────────────────────┐
│ 👤 張小明    📅 2小時前   │ ← Header (40px)
├──────────────────────────┤
│                          │
│ 今天走在公園裡，看到夕陽 │ ← Content區域
│ 西下的美景，心中充滿感恩 │   (自動高度)
│ 。感謝大自然給予我們這樣 │
│ 美好的時刻...           │
│                          │
│ [圖片預覽區域]           │ ← 如果有圖片
│                          │
├──────────────────────────┤
│ 😊 感恩    📷 🎵         │ ← Footer (44px)
│ ❤️ 12  💬 3  📤         │   左：心情標籤+媒體指示
└──────────────────────────┘   右：互動按鈕
```

## 4. 記錄頁面

### 快速記錄模式
```
┌─────────────────────────────────┐
│  ←     新增感恩日記     ✓      │ ← Navigation Bar
├─────────────────────────────────┤
│                                 │
│ ┌─────────────────────────────┐ │ ← 快速輸入框
│ │ 今天有什麼值得感恩的事情？   │ │   (120px高度)
│ │                             │ │
│ │                             │ │
│ │                        🎤   │ │ ← 語音按鈕
│ └─────────────────────────────┘ │
│                                 │
│ 選擇心情：                      │ ← 心情選擇標題
│ 😊 😌 💖 🌟 🙏 😔 😤         │ ← 心情按鈕 (48px)
│                                 │
│ 添加標籤：                      │ ← 標籤區域
│ #感恩 #家庭 #自然 + 添加       │
│                                 │
│                                 │
│                                 │
│                                 │
│                                 │
├─────────────────────────────────┤
│ 📷 📱 🎵 🏷️        [發佈]     │ ← 工具欄
└─────────────────────────────────┘
```

### 詳細編輯模式
```
┌─────────────────────────────────┐
│  ←     編輯日記       ✓        │ ← Navigation Bar
├─────────────────────────────────┤
│                                 │
│ ┌─────────────────────────────┐ │ ← 全屏編輯器
│ │ 今天是特別的一天...         │ │
│ │                             │ │
│ │ 早上醒來，陽光透過窗戶灑進  │ │
│ │ 房間，那一刻我感受到生命的  │ │
│ │ 美好。感謝...               │ │
│ │                             │ │
│ │ [插入的圖片]                │ │
│ │                             │ │
│ │ 繼續寫作...                 │ │
│ │                             │ │
│ └─────────────────────────────┘ │
│                                 │
│ 心情：😊 感恩                   │ ← 心情顯示
│ 標籤：#感恩 #生活 #陽光         │ ← 標籤顯示
│                                 │
├─────────────────────────────────┤
│ B I U 📷 🎵 🏷️      [發佈]   │ ← 格式工具欄
└─────────────────────────────────┘
```

## 5. 專注工作頁面

### 主界面
```
┌─────────────────────────────────┐
│  ←      專注工作      ⚙️       │ ← Navigation Bar
├─────────────────────────────────┤
│                                 │
│                                 │
│           ┌─────────┐           │ ← 圓形計時器
│          ╱           ╲          │   (280px直徑)
│         │    25:00    │         │   中央顯示時間
│         │             │         │
│          ╲  專注工作中 ╱          │   狀態文字
│           └─────────┘           │
│                                 │
│                                 │
│              ⏸️                 │ ← 暫停按鈕 (80px)
│                                 │
│                                 │
│            🔄 重置              │ ← 重置按鈕
│                                 │
│                                 │
│ 今日統計：                      │ ← 統計信息
│ 🎯 專注 3次  ⏰ 總計 90分鐘     │
│                                 │
├─────────────────────────────────┤
│  🏠   ✏️   🎯   👤            │ ← Tab Bar
└─────────────────────────────────┘
```

### 休息提醒彈窗
```
┌─────────────────────────────────┐
│                                 │
│                                 │
│    ┌─────────────────────────┐   │ ← 彈窗 (金色邊框)
│    │                         │   │
│    │         🎉              │   │ ← 慶祝圖標
│    │                         │   │
│    │  謝謝我美好的身體及智慧  │   │ ← 感謝詞
│    │  共同專心的完成了30分鐘  │   │   (H3樣式)
│    │  的工作，現在我們休息    │   │
│    │  10分鐘                 │   │
│    │                         │   │
│    │     🧘‍♀️ 建議活動：       │   │ ← 活動建議
│    │     • 深呼吸 5次        │   │
│    │     • 眼部運動          │   │
│    │     • 簡單伸展          │   │
│    │                         │   │
│    │    [開始休息] [跳過]    │   │ ← 操作按鈕
│    └─────────────────────────┘   │
│                                 │
│                                 │
└─────────────────────────────────┘
```

## 6. 個人中心頁面

### 主界面
```
┌─────────────────────────────────┐
│        個人中心        ⚙️       │ ← Navigation Bar
├─────────────────────────────────┤
│                                 │
│        ┌─────────┐              │ ← 用戶頭像區域
│        │  👤頭像  │              │   (80px圓形)
│        └─────────┘              │
│                                 │
│          張小明                 │ ← 用戶名 (H2)
│       感恩日記 30天             │ ← 統計信息
│                                 │
│ 🏆 🌟 💎 🎯                   │ ← 成就徽章
│                                 │
│ ┌─────────────────────────────┐ │ ← 統計卡片
│ │ 📊 本月統計                 │ │
│ │ 記錄：15篇  點讚：48個      │ │
│ │ 專注：20次  時長：600分鐘   │ │
│ └─────────────────────────────┘ │
│                                 │
│ 📝 我的日記              >     │ ← 功能列表
│ 📊 數據分析              >     │
│ 🔔 通知設定              >     │
│ 🎨 主題設定              >     │
│ ❓ 幫助與反饋            >     │
│                                 │
├─────────────────────────────────┤
│  🏠   ✏️   🎯   👤            │ ← Tab Bar
└─────────────────────────────────┘
```

## 7. 設定頁面

### 主設定頁面
```
┌─────────────────────────────────┐
│  ←        設定                  │ ← Navigation Bar
├─────────────────────────────────┤
│                                 │
│ 個人設定                        │ ← 分組標題
│ 👤 個人資料              >     │
│ 🔒 隱私設定              >     │
│                                 │
│ 通知設定                        │
│ 🔔 日記提醒              >     │
│ ⏰ 專注提醒              >     │
│ 📱 推播通知         [開關]     │
│                                 │
│ 應用設定                        │
│ 🌙 深色模式         [開關]     │
│ 🔤 字體大小              >     │
│ 🌐 語言設定              >     │
│                                 │
│ 其他                            │
│ ❓ 幫助中心              >     │
│ 📧 意見反饋              >     │
│ ℹ️ 關於我們              >     │
│ 🚪 登出                  >     │
│                                 │
└─────────────────────────────────┘
```

## 設計實現注意事項

### 1. 間距系統
- **邊距**: 16px (標準), 24px (大屏)
- **組件間距**: 8px, 12px, 16px, 24px
- **內邊距**: 12px (小), 16px (標準), 20px (大)

### 2. 圓角系統
- **小組件**: 8px
- **按鈕/輸入框**: 12px
- **卡片**: 16px
- **大容器**: 20px

### 3. 陰影系統
- **卡片**: 0 2px 8px rgba(0,0,0,0.1)
- **按鈕**: 0 1px 3px rgba(0,0,0,0.1)
- **浮動元素**: 0 4px 12px rgba(0,0,0,0.15)

### 4. 動畫時長
- **快速**: 100-200ms (按鈕點擊)
- **標準**: 300ms (頁面轉場)
- **慢速**: 500ms (複雜動畫)
