import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>Bar,
  Toolbar,
  IconButton,
  TextField,
  Button,
  Chip,
  Paper,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckIcon from '@mui/icons-material/Check';
import MicIcon from '@mui/icons-material/Mic';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';
import AudiotrackIcon from '@mui/icons-material/Audiotrack';
import LabelIcon from '@mui/icons-material/Label';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const emotions = [
  { id: 'gratitude', label: '感恩', emoji: '🙏', color: '#F59E0B' },
  { id: 'joy', label: '喜悅', emoji: '😊', color: '#FCD34D' },
  { id: 'peace', label: '平靜', emoji: '😌', color: '#8B5CF6' },
  { id: 'love', label: '愛', emoji: '💖', color: '#C084FC' },
  { id: 'hope', label: '希望', emoji: '🌟', color: '#10B981' },
  { id: 'growth', label: '成長', emoji: '🌱', color: '#6B46C1' },
  { id: 'sad', label: '難過', emoji: '😔', color: '#6B7280' },
  { id: 'angry', label: '憤怒', emoji: '😤', color: '#EF4444' },
];

const guidingQuestions = [
  "現在此刻當下，能不能找到愛藏在什麼位置？快記錄下你的感謝",
  "現在此刻當下與你同在的人事物有什麼美麗意義？",
  "今天有什麼小事讓你心生感謝呢？",
  "什麼人或事讓你感到溫暖？",
  "今天你學到了什麼值得感恩的事情？"
];

const CreateDiaryPage = () => {
  const navigate = useNavigate();
  const [content, setContent] = useState('');
  const [selectedEmotion, setSelectedEmotion] = useState('');
  const [tags, setTags] = useState([]);
  const [showGuidingDialog, setShowGuidingDialog] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  const handleBack = () => {
    navigate('/app');
  };

  const handlePublish = () => {
    // 這裡處理發佈邏輯
    console.log('發佈日記:', { content, selectedEmotion, tags });
    navigate('/app');
  };

  const handleEmotionSelect = (emotionId) => {
    setSelectedEmotion(emotionId);
  };

  const handleGuidingQuestionSelect = (question) => {
    setContent(prev => prev + (prev ? '\n\n' : '') + question);
    setShowGuidingDialog(false);
  };

  const handleVoiceRecord = () => {
    setIsRecording(!isRecording);
    // 這裡實現語音錄製邏輯
  };

  const getEmotionColor = (emotionId) => {
    const emotion = emotions.find(e => e.id === emotionId);
    return emotion ? emotion.color : '#6B7280';
  };

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            新增感恩日記
          </Typography>
          <IconButton
            onClick={handlePublish}
            disabled={!content.trim() || !selectedEmotion}
            sx={{
              color: content.trim() && selectedEmotion ? 'primary.main' : 'text.disabled',
            }}
          >
            <CheckIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Box sx={{ p: 2 }}>
        {/* 快速輸入區域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Paper
            sx={{
              p: 2,
              mb: 3,
              borderRadius: 4,
              position: 'relative',
            }}
          >
            <TextField
              multiline
              rows={6}
              fullWidth
              placeholder="今天有什麼值得感恩的事情？"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  border: 'none',
                  '& fieldset': {
                    border: 'none',
                  },
                },
                '& .MuiInputBase-input': {
                  fontSize: '1rem',
                  lineHeight: 1.5,
                },
              }}
            />

            {/* 語音按鈕 */}
            <Fab
              size="small"
              onClick={handleVoiceRecord}
              sx={{
                position: 'absolute',
                bottom: 16,
                right: 16,
                bgcolor: isRecording ? 'error.main' : 'secondary.main',
                color: 'white',
                '&:hover': {
                  bgcolor: isRecording ? 'error.dark' : 'secondary.dark',
                },
              }}
            >
              <MicIcon />
            </Fab>
          </Paper>
        </motion.div>

        {/* 心情選擇 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            選擇心情：
          </Typography>
          <Box
            sx={{
              display: 'flex',
              gap: 1,
              overflowX: 'auto',
              pb: 1,
              mb: 3,
              '&::-webkit-scrollbar': { display: 'none' },
            }}
          >
            {emotions.map((emotion) => (
              <Button
                key={emotion.id}
                onClick={() => handleEmotionSelect(emotion.id)}
                sx={{
                  minWidth: 80,
                  height: 80,
                  borderRadius: '50%',
                  flexDirection: 'column',
                  gap: 0.5,
                  bgcolor: selectedEmotion === emotion.id ? `${emotion.color}20` : 'background.paper',
                  border: `2px solid ${selectedEmotion === emotion.id ? emotion.color : 'transparent'}`,
                  color: selectedEmotion === emotion.id ? emotion.color : 'text.secondary',
                  '&:hover': {
                    bgcolor: `${emotion.color}10`,
                  },
                }}
              >
                <Typography sx={{ fontSize: '1.5rem' }}>
                  {emotion.emoji}
                </Typography>
                <Typography sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                  {emotion.label}
                </Typography>
              </Button>
            ))}
          </Box>
        </motion.div>

        {/* 引導問題按鈕 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Button
            variant="outlined"
            onClick={() => setShowGuidingDialog(true)}
            sx={{
              mb: 3,
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.main',
                color: 'white',
              },
            }}
          >
            💡 需要靈感？點擊獲取引導問題
          </Button>
        </motion.div>

        {/* 標籤區域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            添加標籤：
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 3 }}>
            {['#感恩', '#家庭', '#自然', '#工作', '#朋友', '#健康'].map((tag) => (
              <Chip
                key={tag}
                label={tag}
                onClick={() => {
                  if (tags.includes(tag)) {
                    setTags(tags.filter(t => t !== tag));
                  } else {
                    setTags([...tags, tag]);
                  }
                }}
                variant={tags.includes(tag) ? 'filled' : 'outlined'}
                sx={{
                  bgcolor: tags.includes(tag) ? 'primary.main' : 'transparent',
                  color: tags.includes(tag) ? 'white' : 'text.secondary',
                  '&:hover': {
                    bgcolor: tags.includes(tag) ? 'primary.dark' : 'primary.light',
                    color: 'white',
                  },
                }}
              />
            ))}
          </Box>
        </motion.div>
      </Box>

      {/* 底部工具欄 */}
      <Paper
        sx={{
          position: 'fixed',
          bottom: 80, // 在底部導航上方
          left: 0,
          right: 0,
          p: 2,
          borderRadius: 0,
          borderTop: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-around' }}>
          <IconButton>
            <PhotoCameraIcon />
          </IconButton>
          <IconButton>
            <PhotoLibraryIcon />
          </IconButton>
          <IconButton>
            <AudiotrackIcon />
          </IconButton>
          <IconButton>
            <LabelIcon />
          </IconButton>
          <Button
            variant="contained"
            onClick={handlePublish}
            disabled={!content.trim() || !selectedEmotion}
            sx={{
              px: 3,
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark',
              },
            }}
          >
            發佈
          </Button>
        </Box>
      </Paper>

      {/* 引導問題對話框 */}
      <Dialog
        open={showGuidingDialog}
        onClose={() => setShowGuidingDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>選擇一個引導問題</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {guidingQuestions.map((question, index) => (
              <Button
                key={index}
                variant="outlined"
                onClick={() => handleGuidingQuestionSelect(question)}
                sx={{
                  textAlign: 'left',
                  justifyContent: 'flex-start',
                  p: 2,
                  borderColor: 'primary.light',
                  color: 'text.primary',
                  '&:hover': {
                    bgcolor: 'primary.light',
                    color: 'primary.main',
                  },
                }}
              >
                {question}
              </Button>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowGuidingDialog(false)}>
            取消
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CreateDiaryPage;
