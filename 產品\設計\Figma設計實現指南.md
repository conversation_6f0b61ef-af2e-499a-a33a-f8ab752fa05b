# 感恩日記App - Figma設計實現指南

## 設計文件結構

### 1. 頁面組織
```
感恩日記App設計文件
├── 🎨 Design System
│   ├── Colors (色彩系統)
│   ├── Typography (字體系統)
│   ├── Components (組件庫)
│   └── Icons (圖標庫)
├── 📱 Mobile Screens
│   ├── Onboarding (引導頁)
│   ├── Home (首頁)
│   ├── Create (記錄頁)
│   ├── Focus (專注頁)
│   ├── Profile (個人中心)
│   └── Settings (設定頁)
├── 🔄 User Flows (用戶流程)
└── 📋 Prototypes (原型)
```

## Design System 建立

### 色彩樣式 (Color Styles)
創建以下色彩樣式：

#### 主色調
- `Primary/Purple-600`: #6B46C1
- `Primary/Purple-400`: #A78BFA  
- `Primary/Purple-100`: #EDE9FE
- `Accent/Gold-500`: #F59E0B
- `Accent/Gold-300`: #FCD34D
- `Accent/Gold-100`: #FEF3C7

#### 情感色彩
- `Emotion/Gratitude`: #F59E0B
- `Emotion/Joy`: #FCD34D
- `Emotion/Peace`: #8B5CF6
- `Emotion/Love`: #C084FC
- `Emotion/Hope`: #10B981
- `Emotion/Growth`: #6B46C1

#### 中性色
- `Neutral/Gray-900`: #1F2937
- `Neutral/Gray-500`: #6B7280
- `Neutral/Gray-50`: #F9FAFB
- `Neutral/White`: #FFFFFF

### 文字樣式 (Text Styles)
創建以下文字樣式：

- `Heading/H1`: SF Pro Display Bold 32px
- `Heading/H2`: SF Pro Display Bold 24px
- `Heading/H3`: SF Pro Display Semibold 20px
- `Body/Large`: SF Pro Text Regular 18px
- `Body/Medium`: SF Pro Text Regular 16px
- `Body/Small`: SF Pro Text Regular 14px
- `Caption`: SF Pro Text Regular 12px

### 效果樣式 (Effect Styles)
創建以下效果：

- `Shadow/Card`: Drop shadow (0, 2, 8, rgba(0,0,0,0.1))
- `Shadow/Button`: Drop shadow (0, 1, 3, rgba(0,0,0,0.1))
- `Blur/Background`: Background blur 20px

## 組件庫設計

### 1. 按鈕組件 (Button Component)
#### 變體 (Variants)
- **Type**: Primary, Secondary, Text
- **Size**: Large (48px), Medium (40px), Small (32px)
- **State**: Default, Hover, Pressed, Disabled

#### 設計規範
- **Primary**: 主紫色背景，白色文字，圓角12px
- **Secondary**: 透明背景，主紫色邊框1px，主紫色文字
- **Text**: 透明背景，主紫色文字

### 2. 卡片組件 (Card Component)
#### 變體
- **Type**: Diary Card, Stats Card, User Card
- **Size**: Small, Medium, Large
- **State**: Default, Hover, Selected

#### Diary Card 設計
- **尺寸**: 寬度自適應，高度自動
- **背景**: 白色，圓角16px
- **陰影**: Card shadow
- **內容結構**:
  - Header: 用戶頭像(32px) + 暱稱 + 時間
  - Content: 文字內容 + 媒體內容
  - Footer: 心情標籤 + 互動按鈕

### 3. 輸入框組件 (Input Component)
#### 變體
- **Type**: Text, Search, Textarea
- **State**: Default, Focus, Error, Disabled

#### 設計規範
- **高度**: 48px (單行), 自動 (多行)
- **背景**: 淺灰色 #F9FAFB
- **邊框**: 1px 透明，focus時主紫色
- **圓角**: 12px
- **內邊距**: 16px

### 4. 導航組件
#### Tab Bar
- **高度**: 83px (包含安全區域)
- **背景**: 白色 + 輕微陰影
- **圖標**: 24x24px SF Symbols
- **標籤文字**: Caption 樣式

#### Navigation Bar  
- **高度**: 44px
- **背景**: 半透明白色 + 背景模糊
- **標題**: H3 樣式，居中
- **按鈕**: 32x32px 觸控區域

## 主要頁面設計

### 1. 首頁 - 情感流動卡片牆
#### Frame 設置
- **尺寸**: iPhone 14 Pro (393 x 852)
- **背景**: Neutral/Gray-50

#### 佈局結構
```
Navigation Bar (44px)
├── 搜索圖標 (左)
├── "感恩日記" 標題 (中)
└── 篩選圖標 (右)

情感篩選條 (60px)
├── 水平滾動容器
└── 圓形情感按鈕 (40px直徑)

卡片區域 (自動高度)
├── 雙欄網格佈局
├── 間距: 16px (邊距), 8px (卡片間)
└── 瀑布流排列

Tab Bar (83px)
├── 首頁、記錄、專注、我的
└── 圖標 + 文字標籤
```

#### 卡片設計細節
- **寬度**: (393-32-8)/2 = 176.5px
- **最小高度**: 200px
- **內容間距**: 12px
- **情感光暈**: 2px 漸變邊框

### 2. 記錄頁面
#### 快速記錄區域
- **輸入框**: 全寬，圓角16px，高度120px
- **Placeholder**: "今天有什麼值得感恩的事情？"
- **語音按鈕**: 右下角浮動，金色圓形56px

#### 心情選擇
- **佈局**: 水平滾動
- **按鈕**: 圓形48px，情感色彩背景
- **標籤**: 底部小文字

#### 工具欄
- **位置**: 底部固定
- **按鈕**: 相機、相簿、錄音、標籤
- **發佈按鈕**: 右側主要按鈕

### 3. 專注工作頁面
#### 計時器設計
- **圓形進度條**: 直徑280px，居中
- **進度軌道**: 淺紫色，寬度8px
- **進度條**: 主紫色漸變，寬度8px
- **時間顯示**: H1樣式，白色文字

#### 控制按鈕
- **開始/暫停**: 圓形80px，金色背景
- **重置**: 次要按鈕，底部
- **設定**: 右上角齒輪圖標

#### 狀態指示
- **工作中**: "專注工作中" + 呼吸動畫
- **休息中**: "休息時間" + 放鬆動畫

## 原型設計

### 互動設計
1. **頁面轉場**: Smart Animate，300ms緩動
2. **按鈕點擊**: Scale 0.95，100ms
3. **卡片載入**: Move In從下方，200ms延遲
4. **模態彈出**: Move In從底部，400ms

### 手勢設計
1. **卡片滑動**: 水平滑動查看更多選項
2. **下拉刷新**: 首頁下拉刷新內容
3. **長按**: 卡片長按顯示快速操作

## 響應式設計

### iPhone SE (375px)
- **卡片寬度**: (375-32-8)/2 = 167.5px
- **字體**: 保持原尺寸
- **間距**: 減少至12px邊距

### iPhone 14 Pro Max (430px)  
- **卡片寬度**: (430-32-8)/2 = 195px
- **可考慮**: 三欄佈局選項

### iPad (768px+)
- **佈局**: 三欄或四欄
- **側邊欄**: 左側導航
- **卡片**: 最大寬度240px

## 設計檢查清單

### 視覺一致性
- [ ] 所有色彩使用定義的色彩樣式
- [ ] 所有文字使用定義的文字樣式  
- [ ] 圓角統一使用12px或16px
- [ ] 間距使用8px網格系統

### 可用性檢查
- [ ] 最小觸控區域44x44px
- [ ] 對比度符合WCAG AA標準
- [ ] 重要操作有視覺反饋
- [ ] 錯誤狀態有明確提示

### iOS設計規範
- [ ] 使用SF Pro字體
- [ ] 遵循iOS導航模式
- [ ] 適配安全區域
- [ ] 支援動態字體

## 交付文件

### 設計稿
1. **完整頁面**: 所有主要頁面的設計稿
2. **組件庫**: 可重用的組件系統
3. **原型**: 主要用戶流程的互動原型

### 規範文件
1. **設計規範**: 詳細的視覺規範
2. **組件文檔**: 組件使用說明
3. **切圖資源**: 圖標和插圖資源

### 開發交接
1. **Figma Dev Mode**: 開啟開發者模式
2. **設計標註**: 詳細的尺寸和間距標註
3. **資源導出**: 各種解析度的圖片資源
