import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  Card,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import PersonIcon from '@mui/icons-material/Person';
import LockIcon from '@mui/icons-material/Lock';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PhoneAndroidIcon from '@mui/icons-material/PhoneAndroid';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import LanguageIcon from '@mui/icons-material/Language';
import HelpIcon from '@mui/icons-material/Help';
import EmailIcon from '@mui/icons-material/Email';
import InfoIcon from '@mui/icons-material/Info';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { motion } from 'framer-motion';

const SettingsPage = () => {
  const navigate = useNavigate();
  const [settings, setSettings] = useState({
    pushNotifications: true,
    darkMode: false,
    dailyReminder: true,
    focusReminder: true,
  });
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);

  const handleBack = () => {
    navigate('/app/profile');
  };

  const handleSettingChange = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleLogout = () => {
    setShowLogoutDialog(false);
    // 這裡處理登出邏輯
    navigate('/');
  };

  const settingSections = [
    {
      title: '個人設定',
      items: [
        {
          icon: <PersonIcon />,
          title: '個人資料',
          subtitle: '編輯個人信息',
          action: () => console.log('個人資料'),
          showArrow: true,
        },
        {
          icon: <LockIcon />,
          title: '隱私設定',
          subtitle: '管理隱私和安全',
          action: () => console.log('隱私設定'),
          showArrow: true,
        },
      ],
    },
    {
      title: '通知設定',
      items: [
        {
          icon: <NotificationsIcon />,
          title: '日記提醒',
          subtitle: '每日記錄提醒',
          action: () => console.log('日記提醒'),
          showArrow: true,
        },
        {
          icon: <AccessTimeIcon />,
          title: '專注提醒',
          subtitle: '工作專注提醒',
          action: () => console.log('專注提醒'),
          showArrow: true,
        },
        {
          icon: <PhoneAndroidIcon />,
          title: '推播通知',
          subtitle: '接收應用通知',
          isSwitch: true,
          value: settings.pushNotifications,
          action: () => handleSettingChange('pushNotifications'),
        },
      ],
    },
    {
      title: '應用設定',
      items: [
        {
          icon: <DarkModeIcon />,
          title: '深色模式',
          subtitle: '切換深色主題',
          isSwitch: true,
          value: settings.darkMode,
          action: () => handleSettingChange('darkMode'),
        },
        {
          icon: <TextFieldsIcon />,
          title: '字體大小',
          subtitle: '調整文字大小',
          action: () => console.log('字體大小'),
          showArrow: true,
        },
        {
          icon: <LanguageIcon />,
          title: '語言設定',
          subtitle: '選擇應用語言',
          action: () => console.log('語言設定'),
          showArrow: true,
        },
      ],
    },
    {
      title: '其他',
      items: [
        {
          icon: <HelpIcon />,
          title: '幫助中心',
          subtitle: '常見問題和使用指南',
          action: () => console.log('幫助中心'),
          showArrow: true,
        },
        {
          icon: <EmailIcon />,
          title: '意見反饋',
          subtitle: '提供建議或回報問題',
          action: () => console.log('意見反饋'),
          showArrow: true,
        },
        {
          icon: <InfoIcon />,
          title: '關於我們',
          subtitle: '應用信息和版本',
          action: () => console.log('關於我們'),
          showArrow: true,
        },
        {
          icon: <ExitToAppIcon />,
          title: '登出',
          subtitle: '退出當前帳戶',
          action: () => setShowLogoutDialog(true),
          showArrow: true,
          isDestructive: true,
        },
      ],
    },
  ];

  return (
    <Box sx={{
      bgcolor: 'background.default',
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon sx={{ color: 'text.primary' }} />
          </IconButton>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
            }}
          >
            設定
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto',
      }}>
        {settingSections.map((section, sectionIndex) => (
          <motion.div
            key={section.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: sectionIndex * 0.1 }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                mt: sectionIndex > 0 ? 3 : 0,
                fontWeight: 600,
                color: 'text.secondary',
                fontSize: '0.875rem',
                textTransform: 'uppercase',
                letterSpacing: 0.5,
              }}
            >
              {section.title}
            </Typography>
            <Card sx={{ borderRadius: 3, mb: 2 }}>
              <List sx={{ py: 0 }}>
                {section.items.map((item, index) => (
                  <React.Fragment key={index}>
                    <ListItem
                      button={!item.isSwitch}
                      onClick={item.isSwitch ? undefined : item.action}
                      sx={{
                        py: 2,
                        '&:hover': {
                          bgcolor: item.isSwitch ? 'transparent' : 'action.hover',
                        },
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          color: item.isDestructive ? 'error.main' : 'primary.main',
                          minWidth: 40,
                        }}
                      >
                        {item.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography
                            variant="body1"
                            sx={{
                              fontWeight: 500,
                              color: item.isDestructive ? 'error.main' : 'text.primary',
                            }}
                          >
                            {item.title}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="body2" color="text.secondary">
                            {item.subtitle}
                          </Typography>
                        }
                      />
                      {item.isSwitch ? (
                        <Switch
                          checked={item.value}
                          onChange={item.action}
                          color="primary"
                        />
                      ) : item.showArrow ? (
                        <ChevronRightIcon sx={{ color: 'text.secondary' }} />
                      ) : null}
                    </ListItem>
                    {index < section.items.length - 1 && (
                      <Divider variant="inset" component="li" />
                    )}
                  </React.Fragment>
                ))}
              </List>
            </Card>
          </motion.div>
        ))}

        {/* 版本信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Box sx={{ textAlign: 'center', mt: 4, mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              感恩日記 v1.0.0
            </Typography>
            <Typography variant="caption" color="text.secondary">
              © 2024 感恩日記團隊
            </Typography>
          </Box>
        </motion.div>
      </Box>

      {/* 登出確認對話框 */}
      <Dialog
        open={showLogoutDialog}
        onClose={() => setShowLogoutDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>確認登出</DialogTitle>
        <DialogContent>
          <Typography>
            您確定要登出嗎？登出後需要重新登入才能使用應用。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowLogoutDialog(false)}>
            取消
          </Button>
          <Button onClick={handleLogout} color="error" variant="contained">
            登出
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SettingsPage;
