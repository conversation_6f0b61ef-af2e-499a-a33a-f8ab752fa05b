import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  AppBar,
  Toolbar,
  IconButton,
  Avatar,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Grid,
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import BookIcon from '@mui/icons-material/Book';
import BarChartIcon from '@mui/icons-material/BarChart';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PaletteIcon from '@mui/icons-material/Palette';
import HelpIcon from '@mui/icons-material/Help';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { motion } from 'framer-motion';

const ProfilePage = () => {
  const navigate = useNavigate();

  const userStats = {
    name: '張小明',
    gratitudeDays: 30,
    level: '感恩新手',
    thisMonthEntries: 15,
    thisMonthLikes: 48,
    focusSessions: 20,
    focusMinutes: 600,
  };

  const achievements = [
    { id: 1, name: '初心者', icon: '🌱', color: '#10B981' },
    { id: 2, name: '連續7天', icon: '🔥', color: '#F59E0B' },
    { id: 3, name: '社群之星', icon: '⭐', color: '#FCD34D' },
    { id: 4, name: '專注達人', icon: '🎯', color: '#6B46C1' },
  ];

  const menuItems = [
    {
      icon: <BookIcon />,
      title: '我的日記',
      subtitle: '查看所有日記記錄',
      action: () => console.log('我的日記'),
    },
    {
      icon: <BarChartIcon />,
      title: '數據分析',
      subtitle: '查看情感趨勢和統計',
      action: () => console.log('數據分析'),
    },
    {
      icon: <NotificationsIcon />,
      title: '通知設定',
      subtitle: '管理提醒和通知',
      action: () => console.log('通知設定'),
    },
    {
      icon: <PaletteIcon />,
      title: '主題設定',
      subtitle: '個性化外觀設置',
      action: () => console.log('主題設定'),
    },
    {
      icon: <HelpIcon />,
      title: '幫助與反饋',
      subtitle: '獲取幫助或提供建議',
      action: () => console.log('幫助與反饋'),
    },
  ];

  return (
    <Box sx={{
      bgcolor: 'background.default',
      minHeight: '100vh',
      width: '100%',
      maxWidth: '100vw',
      margin: '0 auto',
    }}>
      {/* 頂部導航欄 */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar sx={{ maxWidth: '600px', width: '100%', margin: '0 auto' }}>
          <Typography
            variant="h3"
            sx={{
              flex: 1,
              color: 'text.primary',
              fontWeight: 600,
              textAlign: 'center',
            }}
          >
            個人中心
          </Typography>
          <IconButton onClick={() => navigate('/app/settings')}>
            <SettingsIcon sx={{ color: 'text.primary' }} />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: 2,
        maxWidth: '600px',
        width: '100%',
        margin: '0 auto'
      }}>
        {/* 用戶信息卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: 'secondary.main',
                  fontSize: '2rem',
                  border: '3px solid',
                  borderColor: 'secondary.light',
                }}
              >
                👤
              </Avatar>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                {userStats.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                感恩日記 {userStats.gratitudeDays} 天 • {userStats.level}
              </Typography>

              {/* 成就徽章 */}
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
                {achievements.map((achievement) => (
                  <Box
                    key={achievement.id}
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      bgcolor: `${achievement.color}20`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1.2rem',
                    }}
                  >
                    {achievement.icon}
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </motion.div>

        {/* 統計卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card sx={{ mb: 3, borderRadius: 4 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                📊 本月統計
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {userStats.thisMonthEntries}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      記錄篇數
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                      {userStats.thisMonthLikes}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      獲得點讚
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {userStats.focusSessions}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      專注次數
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                      {userStats.focusMinutes}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      專注分鐘
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>

        {/* 功能菜單 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card sx={{ borderRadius: 4 }}>
            <List sx={{ py: 0 }}>
              {menuItems.map((item, index) => (
                <ListItem
                  key={index}
                  button
                  onClick={item.action}
                  sx={{
                    py: 2,
                    borderBottom: index < menuItems.length - 1 ? '1px solid' : 'none',
                    borderColor: 'divider',
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: 'primary.main',
                      minWidth: 40,
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {item.title}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        {item.subtitle}
                      </Typography>
                    }
                  />
                  <ChevronRightIcon sx={{ color: 'text.secondary' }} />
                </ListItem>
              ))}
            </List>
          </Card>
        </motion.div>

        {/* 成就展示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Typography variant="h6" sx={{ mt: 3, mb: 2, fontWeight: 600 }}>
            🏆 我的成就
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {achievements.map((achievement) => (
              <Chip
                key={achievement.id}
                label={`${achievement.icon} ${achievement.name}`}
                sx={{
                  bgcolor: `${achievement.color}20`,
                  color: achievement.color,
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: `${achievement.color}30`,
                  },
                }}
              />
            ))}
          </Box>
        </motion.div>
      </Box>
    </Box>
  );
};

export default ProfilePage;
