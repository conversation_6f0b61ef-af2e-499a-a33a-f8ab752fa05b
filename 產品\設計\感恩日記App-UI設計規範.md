# 感恩日記App - UI設計規範

## 設計概述
基於規格-final文件，設計一款優雅的感恩日記App，採用紫色+金色配色方案，貼近iOS設計語言。

## 色彩系統

### 主色調
- **主紫色**: #6B46C1 (深紫色，用於主要按鈕和重要元素)
- **輔助紫色**: #A78BFA (中紫色，用於次要元素)
- **淺紫色**: #EDE9FE (背景色和卡片背景)
- **主金色**: #F59E0B (強調色，用於重要提示和獎勵)
- **輔助金色**: #FCD34D (輔助強調色)
- **淺金色**: #FEF3C7 (背景強調)

### 中性色
- **深灰**: #1F2937 (主要文字)
- **中灰**: #6B7280 (次要文字)
- **淺灰**: #F9FAFB (背景)
- **白色**: #FFFFFF (卡片背景)

### 情感色彩系統
- **感恩/滿足**: 溫暖橙金色 #F59E0B
- **喜悅**: 明亮黃色 #FCD34D
- **平靜**: 柔和藍紫 #8B5CF6
- **愛/溫暖**: 粉紫色 #C084FC
- **希望**: 淺金綠 #10B981
- **成長**: 深紫色 #6B46C1

## 字體系統

### iOS字體
- **標題**: SF Pro Display (Bold, 24-32px)
- **副標題**: SF Pro Display (Semibold, 18-20px)
- **正文**: SF Pro Text (Regular, 16px)
- **說明文字**: SF Pro Text (Regular, 14px)
- **小字**: SF Pro Text (Regular, 12px)

## 組件設計

### 1. 導航欄 (Navigation Bar)
- **高度**: 44px (iOS標準)
- **背景**: 半透明白色 + 模糊效果
- **標題**: SF Pro Display Semibold 18px, 深灰色
- **按鈕**: 主紫色，圓角8px

### 2. 底部標籤欄 (Tab Bar)
- **高度**: 83px (包含安全區域)
- **背景**: 白色 + 輕微陰影
- **圖標**: 24x24px，未選中為中灰，選中為主紫色
- **標籤**: 
  - 首頁 (情感流動卡片牆)
  - 記錄 (新增日記)
  - 專注 (工作提醒)
  - 我的 (個人中心)

### 3. 卡片設計 (情感流動卡片牆)
- **寬度**: 屏幕寬度的45% (雙欄佈局)
- **間距**: 8px
- **圓角**: 16px (iOS風格)
- **陰影**: 輕微陰影 (0 2px 8px rgba(0,0,0,0.1))
- **背景**: 白色
- **情感光暈**: 卡片邊緣2px漸變邊框，對應情感色彩

### 4. 按鈕設計
- **主要按鈕**: 主紫色背景，白色文字，圓角12px，高度48px
- **次要按鈕**: 透明背景，主紫色邊框和文字，圓角12px
- **浮動按鈕**: 主金色背景，白色圖標，圓形56px直徑

## 主要頁面設計

### 1. 啟動頁 (Splash Screen)
- **背景**: 紫色漸變 (#6B46C1 到 #A78BFA)
- **Logo**: 金色感恩符號，居中
- **標語**: "感恩每一刻" (白色文字)

### 2. 首頁 - 情感流動卡片牆
#### 頂部區域
- **導航欄**: 半透明，包含搜索和篩選按鈕
- **情感篩選條**: 水平滾動，圓形情感圖標

#### 卡片區域
- **佈局**: 瀑布流雙欄
- **卡片內容**:
  - 頂部: 用戶頭像 + 暱稱 + 時間
  - 中間: 日記內容預覽 + 圖片
  - 底部: 心情圖標 + 互動按鈕 (點讚、評論、分享)
- **多媒體指示器**: 右上角小圖標 (圖片、音訊、視頻)

### 3. 記錄頁面
#### 輸入區域
- **快速輸入**: 頂部圓角輸入框，placeholder: "快速記錄感恩..."
- **語音按鈕**: 金色圓形按鈕，麥克風圖標
- **心情選擇**: 水平滾動的情感圓形按鈕

#### 編輯區域
- **文字編輯器**: 全屏編輯，支援富文本
- **媒體上傳**: 底部工具欄 (相機、相簿、錄音)
- **標籤添加**: 可輸入和選擇的標籤系統

### 4. 專注工作頁面
#### 計時器區域
- **圓形進度條**: 大型圓形，紫色進度，金色強調
- **時間顯示**: 中央大字體顯示剩餘時間
- **狀態指示**: "專注中" 或 "休息中"

#### 控制區域
- **開始/暫停**: 大型圓形按鈕，主金色
- **設定按鈕**: 右上角齒輪圖標
- **歷史記錄**: 底部小卡片顯示今日統計

#### 提醒設定
- **時間滑桿**: iOS風格滑桿，紫色軌道
- **感謝詞編輯**: 文字輸入框，支援變數
- **通知設定**: 開關按鈕

### 5. 個人中心
#### 頭部區域
- **用戶頭像**: 大型圓形頭像，金色邊框
- **用戶資訊**: 暱稱、感恩天數、等級
- **成就徽章**: 水平滾動的徽章展示

#### 功能列表
- **我的日記**: 列表項目，右箭頭
- **數據分析**: 圖表圖標
- **設定**: 齒輪圖標
- **關於**: 資訊圖標

## 動畫效果

### 1. 頁面轉場
- **推入動畫**: iOS標準右滑推入
- **模態彈出**: 從底部滑入，圓角頂部

### 2. 卡片動畫
- **載入**: 從下方淡入，輕微彈跳
- **點擊**: 輕微縮放 (0.95倍)
- **滑動**: 視差效果

### 3. 按鈕動畫
- **點擊**: 縮放動畫 + 觸覺反饋
- **載入**: 旋轉動畫

## 響應式設計

### iPhone (375-428px)
- **卡片**: 雙欄佈局
- **間距**: 16px 邊距，8px 卡片間距

### iPad (768px+)
- **卡片**: 三欄佈局
- **側邊欄**: 左側導航欄
- **間距**: 24px 邊距，12px 卡片間距

## 無障礙設計
- **對比度**: 符合WCAG AA標準
- **字體大小**: 支援動態字體
- **語音**: VoiceOver支援
- **觸控**: 最小44px觸控區域

## 圖標系統
- **風格**: SF Symbols風格，線性圖標
- **大小**: 24x24px (標準)，32x32px (大型)
- **顏色**: 主紫色 (選中)，中灰色 (未選中)

## 狀態設計
- **載入**: 紫色旋轉指示器
- **空狀態**: 金色插圖 + 引導文字
- **錯誤**: 溫和的錯誤提示，不使用紅色
- **成功**: 金色勾選圖標 + 慶祝動畫

## 詳細頁面佈局

### 6. 日記詳情頁
#### 頂部區域
- **導航欄**: 返回按鈕 + 分享按鈕
- **用戶資訊**: 頭像、暱稱、發佈時間
- **心情標籤**: 彩色圓形標籤，對應情感色彩

#### 內容區域
- **文字內容**: 可滾動文字區域，行間距1.5
- **媒體內容**: 圖片輪播、音訊播放器、視頻播放器
- **標籤**: 圓角標籤，淺紫色背景

#### 互動區域
- **點讚按鈕**: 心形圖標，點擊有動畫效果
- **評論區**: 列表顯示，支援回覆
- **分享選項**: 底部彈出選單

### 7. 數據分析頁
#### 統計卡片
- **今日統計**: 白色卡片，圓角16px
- **週統計**: 柱狀圖，紫色漸變
- **月統計**: 環形圖，多色情感分佈

#### 情感趨勢圖
- **折線圖**: 紫色線條，金色節點
- **時間軸**: 底部可滑動選擇時間範圍
- **情感標記**: 不同顏色點標記重要事件

### 8. 設定頁面
#### 個人設定
- **頭像設定**: 大型圓形頭像，可點擊更換
- **暱稱編輯**: 輸入框，即時驗證
- **隱私設定**: 開關列表

#### 通知設定
- **工作提醒**: 時間選擇器
- **日記提醒**: 每日提醒時間設定
- **社群通知**: 點讚、評論通知開關

#### 應用設定
- **主題模式**: 淺色/深色切換
- **字體大小**: 滑桿調整
- **語言設定**: 列表選擇

### 9. 引導頁面
#### 歡迎頁面
- **插圖**: 金色線條插圖，感恩主題
- **標題**: "開始你的感恩之旅"
- **說明**: 簡潔的功能介紹

#### 功能介紹
- **滑動卡片**: 3-4張介紹卡片
- **進度指示**: 底部圓點指示器
- **跳過按鈕**: 右上角，可選擇跳過

#### 權限請求
- **通知權限**: 溫和的說明文字
- **相機權限**: 功能說明 + 好處
- **麥克風權限**: 語音記錄說明

## 特殊功能設計

### 工作專注模式
#### 專注狀態
- **全屏模式**: 隱藏其他干擾元素
- **呼吸動畫**: 圓形呼吸引導動畫
- **環境音**: 可選背景音樂

#### 休息提醒
- **彈窗設計**: 圓角卡片，金色邊框
- **感謝詞顯示**: 大字體，居中顯示
- **動作建議**: 簡單的伸展動作圖示

### 感謝卡片分享
#### 卡片設計
- **背景模板**: 多種漸變背景可選
- **文字排版**: 優雅的字體排列
- **裝飾元素**: 金色線條和圖案

#### 分享選項
- **社群平台**: FB、LINE、Instagram等
- **儲存相簿**: 高解析度圖片
- **複製連結**: 分享到其他平台

## 微互動設計

### 點讚動畫
- **心形放大**: 從小到大的彈跳動畫
- **粒子效果**: 金色小星星散開
- **觸覺反饋**: 輕微震動

### 發佈成功
- **勾選動畫**: 圓形到勾選的變形
- **慶祝效果**: 彩色紙屑飄落
- **成就提示**: 如果達成里程碑

### 載入狀態
- **骨架屏**: 卡片形狀的灰色佔位符
- **漸進載入**: 內容逐步顯示
- **錯誤重試**: 溫和的重試按鈕

## 深色模式適配

### 色彩調整
- **主紫色**: 調亮至 #8B5CF6
- **背景色**: 深灰 #1F2937
- **卡片背景**: 中深灰 #374151
- **文字色**: 淺灰 #F9FAFB

### 對比度優化
- **確保可讀性**: 所有文字符合對比度要求
- **金色調整**: 在深色背景下的可見性
- **陰影調整**: 使用邊框替代陰影效果
